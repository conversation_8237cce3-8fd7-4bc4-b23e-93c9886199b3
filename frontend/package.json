{"name": "rolewise", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@auth0/auth0-react": "^2.3.0", "@azure/msal-browser": "^4.13.0", "@supabase/supabase-js": "^2.49.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vanilla-extract/css": "^1.17.2", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "reactflow": "^11.11.4", "vitest": "^3.2.2", "zustand": "^5.0.5"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@types/node": "^22.15.29", "@vanilla-extract/vite-plugin": "^5.0.3", "@vitejs/plugin-react": "^4.5.1", "jsdom": "^26.1.0", "vite": "^6.3.5"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "main": "index.js", "keywords": [], "author": "", "license": "ISC", "description": ""}
import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import * as styles from './Navbar.css';

const Navbar = () => {
  const navigate = useNavigate();
  const { isLoading, isAuthenticated, user, logout } = useAuth0();

  return (
    <nav className={styles.navbarStyle}>
      <div className={styles.navbarLeftStyle}>
        <div className={styles.logoContainerStyle}>
          <span className={styles.logoTextStyle}>R</span>
        </div>
        <span className={styles.userNameStyle}>Rolewise.ai</span>
      </div>

      <div className={styles.navbarRightStyle}>
        <div className={styles.userButtonContainer}>
          {!isLoading && isAuthenticated && user && (
            <>
              <span className={styles.emailTextStyle}>
                {user.email || user.name || 'User'}
              </span>
              <button
                onClick={() => logout({
                  logoutParams: {
                    returnTo: window.location.origin + '/login'
                  }
                })}
                className={styles.signOutButtonStyle}
              >
                Logout
              </button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
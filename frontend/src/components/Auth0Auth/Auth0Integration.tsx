import { useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useAuthStore, initializeUserProfile, clearAuthState } from '../../stores/authStore';
import { AuthStatus } from '../../types/auth';

/**
 * Component that synchronizes Auth0 state with our Zustand auth store
 * This should be rendered at the top level of the app
 */
export const Auth0Integration: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { 
    isLoading, 
    isAuthenticated, 
    user, 
    getAccessTokenSilently,
    error 
  } = useAuth0();
  
  const { status, setStatus } = useAuthStore();

  useEffect(() => {
    const syncAuthState = async () => {
      if (isLoading) {
        setStatus(AuthStatus.LOADING);
        return;
      }

      if (error) {
        console.error('Auth0 error:', error);
        setStatus(AuthStatus.UNAUTHENTICATED);
        clearAuthState();
        return;
      }

      if (isAuthenticated && user) {
        try {
          // Get the access token for API calls
          const accessToken = await getAccessTokenSilently();
          
          // Initialize user profile and sync with our store
          await initializeUserProfile(user, accessToken);
        } catch (tokenError) {
          console.error('Error getting access token:', tokenError);
          setStatus(AuthStatus.UNAUTHENTICATED);
          clearAuthState();
        }
      } else {
        setStatus(AuthStatus.UNAUTHENTICATED);
        clearAuthState();
      }
    };

    syncAuthState();
  }, [isLoading, isAuthenticated, user, error, getAccessTokenSilently, setStatus]);

  return <>{children}</>;
};

export default Auth0Integration;

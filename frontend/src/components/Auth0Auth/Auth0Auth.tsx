import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth0 } from '@auth0/auth0-react';
import * as styles from './Auth0Auth.css';

/**
 * Component that requires authentication to access
 * Redirects to login page if user is not authenticated
 */
export const RequireAuth: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoading, isAuthenticated, error } = useAuth0();

  if (isLoading) {
    return <div>Loading authentication...</div>;
  }

  if (error) {
    console.error('Auth0 error:', error);
    return <div>Authentication error: {error.message}</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

/**
 * Layout for protected routes
 * Includes the main application layout with sidebar and navbar
 */
export const ProtectedLayout = () => {
  return (
    <RequireAuth>
      <Outlet />
    </RequireAuth>
  );
};

/**
 * Authentication buttons for the navbar
 */
export const AuthButtons = () => {
  const { isLoading, isAuthenticated, loginWithRedirect, logout } = useAuth0();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className={styles.authButtonsContainer}>
      {!isAuthenticated ? (
        <button 
          onClick={() => loginWithRedirect()}
          className={styles.authButton}
        >
          Log In
        </button>
      ) : (
        <button 
          onClick={() => logout({ logoutParams: { returnTo: window.location.origin + '/login' } })}
          className={styles.authButton}
        >
          Log Out
        </button>
      )}
    </div>
  );
};

export default {
  RequireAuth,
  ProtectedLayout,
  AuthButtons
};

// Explicitly import React to ensure it's available
import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { Auth0Provider } from '@auth0/auth0-react';
import App from './App';
import './styles/globalStyles.css.ts';



// Get Auth0 configuration from environment variables
const AUTH0_DOMAIN = import.meta.env.VITE_AUTH0_DOMAIN;
const AUTH0_CLIENT_ID = import.meta.env.VITE_AUTH0_CLIENT_ID;
const AUTH0_AUDIENCE = import.meta.env.VITE_AUTH0_AUDIENCE;

// Check if Auth0 configuration is available
if (!AUTH0_DOMAIN || !AUTH0_CLIENT_ID) {
  throw new Error('Missing Auth0 configuration. Please check your environment variables.');
}

// Configure future flags for React Router v7
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

// Get the root element and create the root
const rootElement = document.getElementById('root');

if (!rootElement) {
  console.error('Root element not found!');
} else {
  const root = ReactDOM.createRoot(rootElement);

  // Wrap rendering in try/catch to catch any errors
  try {
    root.render(
      <React.StrictMode>
        <Auth0Provider
          domain={AUTH0_DOMAIN}
          clientId={AUTH0_CLIENT_ID}
          authorizationParams={{
            redirect_uri: window.location.origin,
            audience: AUTH0_AUDIENCE,
          }}
          useRefreshTokens={true}
          cacheLocation="localstorage"
        >
          <BrowserRouter {...router}>
            <App />
          </BrowserRouter>
        </Auth0Provider>
      </React.StrictMode>
    );
  } catch (error) {
    console.error('Error rendering React application:', error);

    // Show a fallback UI if rendering fails
    rootElement.innerHTML = `
      <div style="padding: 20px; margin: 20px; background-color: #ffebee; border: 1px solid #ffcdd2; border-radius: 5px;">
        <h2>Error Rendering Application</h2>
        <p>An error occurred while rendering the application:</p>
        <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;">${error}</pre>
      </div>
    `;
  }
}